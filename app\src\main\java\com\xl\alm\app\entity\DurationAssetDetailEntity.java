package com.xl.alm.app.entity;

import com.jd.lightning.common.core.domain.BaseEntity;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 久期资产明细表实体类
 *
 * <AUTHOR> Assistant
 */
@Data
public class DurationAssetDetailEntity extends BaseEntity {
    
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 账期
     */
    private String accountPeriod;

    /**
     * 资产编号
     */
    private Integer assetNumber;

    /**
     * 账户名称
     */
    private String accountName;

    /**
     * 资产名称
     */
    private String assetName;

    /**
     * 证券代码
     */
    private String securityCode;

    /**
     * 资产小小类
     */
    private String assetSubCategory;

    /**
     * 持仓面值
     */
    private BigDecimal holdingFaceValue;

    /**
     * 市值
     */
    private BigDecimal marketValue;

    /**
     * 账面余额
     */
    private BigDecimal bookBalance;

    /**
     * 账面价值
     */
    private BigDecimal bookValue;

    /**
     * 票面利率
     */
    private BigDecimal couponRate;

    /**
     * 付息方式
     */
    private String paymentMethod;

    /**
     * 折现曲线标识
     */
    private String curveId;

    /**
     * 调整起息日
     */
    private Date adjustedValueDate;

    /**
     * 调整买入日
     */
    private Date adjustedPurchaseDate;

    /**
     * 调整到期日
     */
    private Date adjustedMaturityDate;

    /**
     * 发行时点价差计算标识
     */
    private String issueSpreadCalcFlag;

    /**
     * 利差久期资产统计标识
     */
    private String spreadDurationStatFlag;

    /**
     * 评估时点价差
     */
    private BigDecimal evalSpread;

    /**
     * 利差久期
     */
    private BigDecimal spreadDuration;

    /**
     * 账面价值σ9%
     */
    private BigDecimal bookValueSigma9;

    /**
     * 账面价值σ17%
     */
    private BigDecimal bookValueSigma17;

    /**
     * 账面价值σ77%
     */
    private BigDecimal bookValueSigma77;

    /**
     * 发行时点资产现值
     */
    private BigDecimal issuePresentValue;

    /**
     * 发行时点价差
     */
    private BigDecimal issueSpread;

    /**
     * 评估时点资产现值
     */
    private BigDecimal evalPresentValue;

    /**
     * 评估时点到期收益率
     */
    private BigDecimal evalMaturityYield;

    /**
     * 资产修正久期
     */
    private BigDecimal assetModifiedDuration;

    /**
     * 评估时点资产现值+50bp
     */
    private BigDecimal evalPresentValuePlus50bp;

    /**
     * 评估时点资产现值-50bp
     */
    private BigDecimal evalPresentValueMinus50bp;

    /**
     * 资产有效久期
     */
    private BigDecimal assetEffectiveDuration;

    // DV10系列字段
    /**
     * DV10_0
     */
    private BigDecimal dv101;

    /**
     * DV10_0.5
     */
    private BigDecimal dv102;

    /**
     * DV10_1
     */
    private BigDecimal dv103;

    /**
     * DV10_2
     */
    private BigDecimal dv104;

    /**
     * DV10_3
     */
    private BigDecimal dv105;

    /**
     * DV10_4
     */
    private BigDecimal dv106;

    /**
     * DV10_5
     */
    private BigDecimal dv107;

    /**
     * DV10_6
     */
    private BigDecimal dv108;

    /**
     * DV10_7
     */
    private BigDecimal dv109;

    /**
     * DV10_8
     */
    private BigDecimal dv1010;

    /**
     * DV10_10
     */
    private BigDecimal dv1011;

    /**
     * DV10_12
     */
    private BigDecimal dv1012;

    /**
     * DV10_15
     */
    private BigDecimal dv1013;

    /**
     * DV10_20
     */
    private BigDecimal dv1014;

    /**
     * DV10_25
     */
    private BigDecimal dv1015;

    /**
     * DV10_30
     */
    private BigDecimal dv1016;

    /**
     * DV10_35
     */
    private BigDecimal dv1017;

    /**
     * DV10_40
     */
    private BigDecimal dv1018;

    /**
     * DV10_45
     */
    private BigDecimal dv1019;

    /**
     * DV10_50
     */
    private BigDecimal dv1020;

    // DV10上升系列字段
    /**
     * DV10_0_上升
     */
    private BigDecimal dv101Up;

    /**
     * DV10_0.5_上升
     */
    private BigDecimal dv102Up;

    /**
     * DV10_1_上升
     */
    private BigDecimal dv103Up;

    /**
     * DV10_2_上升
     */
    private BigDecimal dv104Up;

    /**
     * DV10_3_上升
     */
    private BigDecimal dv105Up;

    /**
     * DV10_4_上升
     */
    private BigDecimal dv106Up;

    /**
     * DV10_5_上升
     */
    private BigDecimal dv107Up;

    /**
     * DV10_6_上升
     */
    private BigDecimal dv108Up;

    /**
     * DV10_7_上升
     */
    private BigDecimal dv109Up;

    /**
     * DV10_8_上升
     */
    private BigDecimal dv1010Up;

    /**
     * DV10_10_上升
     */
    private BigDecimal dv1011Up;

    /**
     * DV10_12_上升
     */
    private BigDecimal dv1012Up;

    /**
     * DV10_15_上升
     */
    private BigDecimal dv1013Up;

    /**
     * DV10_20_上升
     */
    private BigDecimal dv1014Up;

    /**
     * DV10_25_上升
     */
    private BigDecimal dv1015Up;

    /**
     * DV10_30_上升
     */
    private BigDecimal dv1016Up;

    /**
     * DV10_35_上升
     */
    private BigDecimal dv1017Up;

    /**
     * DV10_40_上升
     */
    private BigDecimal dv1018Up;

    /**
     * DV10_45_上升
     */
    private BigDecimal dv1019Up;

    /**
     * DV10_50_上升
     */
    private BigDecimal dv1020Up;

    // DV10下降系列字段
    /**
     * DV10_0_下降
     */
    private BigDecimal dv101Down;

    /**
     * DV10_0.5_下降
     */
    private BigDecimal dv102Down;

    /**
     * DV10_1_下降
     */
    private BigDecimal dv103Down;

    /**
     * DV10_2_下降
     */
    private BigDecimal dv104Down;

    /**
     * DV10_3_下降
     */
    private BigDecimal dv105Down;

    /**
     * DV10_4_下降
     */
    private BigDecimal dv106Down;

    /**
     * DV10_5_下降
     */
    private BigDecimal dv107Down;

    /**
     * DV10_6_下降
     */
    private BigDecimal dv108Down;

    /**
     * DV10_7_下降
     */
    private BigDecimal dv109Down;

    /**
     * DV10_8_下降
     */
    private BigDecimal dv1010Down;

    /**
     * DV10_10_下降
     */
    private BigDecimal dv1011Down;

    /**
     * DV10_12_下降
     */
    private BigDecimal dv1012Down;

    /**
     * DV10_15_下降
     */
    private BigDecimal dv1013Down;

    /**
     * DV10_20_下降
     */
    private BigDecimal dv1014Down;

    /**
     * DV10_25_下降
     */
    private BigDecimal dv1015Down;

    /**
     * DV10_30_下降
     */
    private BigDecimal dv1016Down;

    /**
     * DV10_35_下降
     */
    private BigDecimal dv1017Down;

    /**
     * DV10_40_下降
     */
    private BigDecimal dv1018Down;

    /**
     * DV10_45_下降
     */
    private BigDecimal dv1019Down;

    /**
     * DV10_50_下降
     */
    private BigDecimal dv1020Down;

    // 现金流字段
    /**
     * 发行时点现金流值集
     */
    private String issueCashflowSet;

    /**
     * 评估时点现金流值集
     */
    private String evalCashflowSet;

    /**
     * 是否删除，0:否，1:是
     */
    private Integer isDel = 0;
}

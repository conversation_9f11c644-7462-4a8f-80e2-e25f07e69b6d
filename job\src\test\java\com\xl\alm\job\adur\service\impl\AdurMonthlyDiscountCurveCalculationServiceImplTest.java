package com.xl.alm.job.adur.service.impl;

import com.xl.alm.job.adur.entity.AdurAnnualDiscountCurveEntity;
import com.xl.alm.job.adur.entity.AdurDurationAssetDetailEntity;
import com.xl.alm.job.adur.entity.AdurMonthlyDiscountCurveEntity;
import com.xl.alm.job.adur.mapper.AdurAnnualDiscountCurveMapper;
import com.xl.alm.job.adur.mapper.AdurDurationAssetDetailMapper;
import com.xl.alm.job.adur.mapper.AdurMonthlyDiscountCurveMapper;
import com.xl.alm.job.adur.util.TermDataUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 月度折现曲线计算服务测试类
 *
 * <AUTHOR> Assistant
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
class AdurMonthlyDiscountCurveCalculationServiceImplTest {

    @Autowired
    private AdurMonthlyDiscountCurveCalculationServiceImpl calculationService;

    @Autowired
    private AdurMonthlyDiscountCurveMapper monthlyDiscountCurveMapper;

    @Autowired
    private AdurAnnualDiscountCurveMapper annualDiscountCurveMapper;

    @Autowired
    private AdurDurationAssetDetailMapper durationAssetDetailMapper;

    private String testAccountPeriod = "202501";
    private Integer testAssetNumber = 1;

    @BeforeEach
    void setUp() {
        // 清理测试数据
        monthlyDiscountCurveMapper.deleteByAccountPeriod(testAccountPeriod);
        annualDiscountCurveMapper.deleteByAccountPeriod(testAccountPeriod);
        durationAssetDetailMapper.deleteByAccountPeriod(testAccountPeriod);
    }

    @Test
    @DisplayName("测试月度折现曲线计算 - 折现曲线标识=0且日期类型=评估时点")
    void testCalculateMonthlyDiscountCurve_CurveId0_EvaluationType() {
        // 准备测试数据
        prepareTestData_CurveId0();

        // 执行计算
        boolean result = calculationService.calculateMonthlyDiscountCurve(testAccountPeriod);

        // 验证结果
        assertTrue(result, "计算应该成功");

        // 验证数据库中的数据
        List<AdurMonthlyDiscountCurveEntity> savedData = monthlyDiscountCurveMapper.selectByAccountPeriod(testAccountPeriod);
        assertNotNull(savedData);
        assertFalse(savedData.isEmpty());

        // 查找评估时点的数据
        AdurMonthlyDiscountCurveEntity evaluationEntity = savedData.stream()
                .filter(entity -> "评估时点".equals(entity.getDateType()))
                .findFirst()
                .orElse(null);

        assertNotNull(evaluationEntity, "应该有评估时点的数据");

        // 验证JSON字段不为空
        assertNotNull(evaluationEntity.getMonthlyDiscountRateSet(), "月度折现曲线利率值集不应该为空");
        assertFalse(evaluationEntity.getMonthlyDiscountRateSet().trim().isEmpty(), "月度折现曲线利率值集不应该为空字符串");

        // 验证JSON格式正确
        Map<Integer, BigDecimal> termValues = TermDataUtil.parseTermValues(evaluationEntity.getMonthlyDiscountRateSet());
        assertNotNull(termValues, "应该能够解析JSON数据");
        assertFalse(termValues.isEmpty(), "解析后的期限数据不应该为空");

        // 验证包含期限0到600的数据
        assertEquals(601, termValues.size(), "应该包含0-600共601个期限的数据");
        assertTrue(termValues.containsKey(0), "应该包含期限0的数据");
        assertTrue(termValues.containsKey(600), "应该包含期限600的数据");

        // 验证所有期限的值都等于到期收益率（因为折现曲线标识=0且日期类型=评估时点）
        BigDecimal expectedYield = new BigDecimal("0.045000"); // 测试数据中设置的到期收益率
        for (int i = 0; i <= 600; i++) {
            assertEquals(expectedYield, termValues.get(i), "期限" + i + "的收益率应该等于到期收益率");
        }

        System.out.println("测试完成，JSON数据示例：" + evaluationEntity.getMonthlyDiscountRateSet().substring(0, Math.min(200, evaluationEntity.getMonthlyDiscountRateSet().length())) + "...");
    }

    @Test
    @DisplayName("测试月度折现曲线计算 - 折现曲线标识≠0")
    void testCalculateMonthlyDiscountCurve_CurveIdNonZero() {
        // 准备测试数据
        prepareTestData_CurveIdNonZero();

        // 执行计算
        boolean result = calculationService.calculateMonthlyDiscountCurve(testAccountPeriod);

        // 验证结果
        assertTrue(result, "计算应该成功");

        // 验证数据库中的数据
        List<AdurMonthlyDiscountCurveEntity> savedData = monthlyDiscountCurveMapper.selectByAccountPeriod(testAccountPeriod);
        assertNotNull(savedData);
        assertFalse(savedData.isEmpty());

        // 查找评估时点的数据
        AdurMonthlyDiscountCurveEntity evaluationEntity = savedData.stream()
                .filter(entity -> "评估时点".equals(entity.getDateType()))
                .findFirst()
                .orElse(null);

        assertNotNull(evaluationEntity, "应该有评估时点的数据");

        // 验证JSON字段不为空
        assertNotNull(evaluationEntity.getMonthlyDiscountRateSet(), "月度折现曲线利率值集不应该为空");
        assertFalse(evaluationEntity.getMonthlyDiscountRateSet().trim().isEmpty(), "月度折现曲线利率值集不应该为空字符串");

        // 验证JSON格式正确
        Map<Integer, BigDecimal> termValues = TermDataUtil.parseTermValues(evaluationEntity.getMonthlyDiscountRateSet());
        assertNotNull(termValues, "应该能够解析JSON数据");
        assertEquals(601, termValues.size(), "应该包含0-600共601个期限的数据");

        // 验证期限12（1年）的值应该等于年度折现曲线的term1
        BigDecimal term12Value = termValues.get(12);
        assertNotNull(term12Value, "期限12的值不应该为空");
        assertEquals(new BigDecimal("0.035000"), term12Value, "期限12的值应该等于年度曲线的1年期利率");

        // 验证期限24（2年）的值应该等于年度折现曲线的term2
        BigDecimal term24Value = termValues.get(24);
        assertNotNull(term24Value, "期限24的值不应该为空");
        assertEquals(new BigDecimal("0.040000"), term24Value, "期限24的值应该等于年度曲线的2年期利率");

        System.out.println("测试完成，JSON数据示例：" + evaluationEntity.getMonthlyDiscountRateSet().substring(0, Math.min(200, evaluationEntity.getMonthlyDiscountRateSet().length())) + "...");
    }

    /**
     * 准备测试数据 - 折现曲线标识=0
     */
    private void prepareTestData_CurveId0() {
        // 创建久期资产明细数据
        AdurDurationAssetDetailEntity assetDetail = new AdurDurationAssetDetailEntity();
        assetDetail.setAccountPeriod(testAccountPeriod);
        assetDetail.setAssetNumber(testAssetNumber);
        assetDetail.setCurveId("0"); // 折现曲线标识=0
        assetDetail.setEvalMaturityYield(new BigDecimal("0.045000")); // 评估时点到期收益率4.5%
        assetDetail.setAccountName("测试账户");
        assetDetail.setAssetName("测试资产");
        assetDetail.setSecurityCode("TEST001");

        durationAssetDetailMapper.batchInsertDurationAssetDetail(Arrays.asList(assetDetail));
    }

    /**
     * 准备测试数据 - 折现曲线标识≠0
     */
    private void prepareTestData_CurveIdNonZero() {
        // 创建久期资产明细数据
        AdurDurationAssetDetailEntity assetDetail = new AdurDurationAssetDetailEntity();
        assetDetail.setAccountPeriod(testAccountPeriod);
        assetDetail.setAssetNumber(testAssetNumber);
        assetDetail.setCurveId("1"); // 折现曲线标识≠0
        assetDetail.setEvalMaturityYield(new BigDecimal("0.045000"));
        assetDetail.setAccountName("测试账户");
        assetDetail.setAssetName("测试资产");
        assetDetail.setSecurityCode("TEST001");

        durationAssetDetailMapper.batchInsertDurationAssetDetail(Arrays.asList(assetDetail));

        // 创建年度折现曲线数据
        AdurAnnualDiscountCurveEntity annualCurve = new AdurAnnualDiscountCurveEntity();
        annualCurve.setAccountPeriod(testAccountPeriod);
        annualCurve.setDateType("评估时点");
        annualCurve.setDate(new Date());
        annualCurve.setAssetNumber(Integer.valueOf(testAssetNumber));
        annualCurve.setAccountName("测试账户");
        annualCurve.setAssetName("测试资产");
        annualCurve.setSecurityCode("TEST001");
        annualCurve.setCurveId("1");

        // 设置年度利率数据
        annualCurve.setTerm0(new BigDecimal("0.030000")); // 0年：3%
        annualCurve.setTerm1(new BigDecimal("0.035000")); // 1年：3.5%
        annualCurve.setTerm2(new BigDecimal("0.040000")); // 2年：4%
        annualCurve.setTerm3(new BigDecimal("0.045000")); // 3年：4.5%

        annualDiscountCurveMapper.batchInsertAnnualDiscountCurve(Arrays.asList(annualCurve));
    }
}
